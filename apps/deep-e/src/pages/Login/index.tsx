import { Button, Form, Input, App, Modal, ConfigProvider } from 'antd';
import React, { useEffect, useState } from 'react';
import useLogin from './hooks/uselogin';
import ajax from '@/api';
import { useDispatch } from 'react-redux';
// import { setToken, setUser } from '@/store/features/loginSlice';
import { useAuthStore, getToken } from '@/store/features/auth';
import { useNavigate } from 'react-router-dom';
import { i18nScope } from '@/languages';
import _ from 'lodash';
import CryptoJS from 'crypto-js';
import { persistor } from '@/store/index.ts';
import { setShowLoginConfirm } from '@/store/features/loginSlice';
import { setAgainLoginVisible } from '@/store/features/layoutSlice';
import { setShowbeLoginCovered } from '@/store/features/layoutSlice';
import { useSelector } from 'react-redux';
import { useAliveController } from 'react-activation';
import store from '@/store';

interface CaptchaDataType {
  captchaId: string;
  picPath: string;
  msg: string;
}

const random = Math.random().toString();
function Login() {
  const navigate = useNavigate();
  // const { clear } = useAliveController();
  const { message } = App.useApp();
  const { setUser, setToken } = useAuthStore();
  if (getToken() !== null) {
    navigate('/');
  }
  const dispatch = useDispatch();
  const [form] = Form.useForm();
  const [loading, setLoading] = useState(false);
  const [aesKey, setAesKey] = useState<string>('');
  const { login } = useLogin<CaptchaDataType>();
  // const [captchaData, setCaptchaData] = useState<CaptchaDataType>({
  //   captchaId: '',
  //   picPath: '',
  //   msg: '',
  // });
  const [disableParea, setDisableParea] = useState(true);
  const [modalOkLoading, setModalOkLoading] = useState(false);

  useEffect(() => {
    store.dispatch(setShowLoginConfirm(false));
    store.dispatch(setShowbeLoginCovered(false));
    dispatch(setAgainLoginVisible(false));
  }, []);

  /**
   * @description 获取aes加密的key
   * */
  const getAesKey = async () => {
    try {
      const res = await ajax.getAesKey(random);
      const data = _.get(res, 'data', '') as string;
      setAesKey(data);
    } catch (err) {
      console.error(err);
    }
  };

  /**
   * @description 密码加密函数
   * */

  const enCryptAES = ({ psw, sKey }: { psw: string; sKey: string }) => {
    const _sKey = CryptoJS.enc.Utf8.parse(sKey);
    const encrypt = CryptoJS.AES.encrypt(psw, _sKey, {
      iv: _sKey,
      mode: CryptoJS.mode.CBC,
      padding: CryptoJS.pad.Pkcs7,
    });
    return encrypt.toString();
  };

  const getLoginData = () => {
    const values = form.getFieldsValue();
    return {
      username: values.username,
      password: enCryptAES({ psw: values.password, sKey: aesKey }),
      coCode: Number(values.coCode),
      randomString: random,
      // captcha: values.captcha,
      // captchaId: captchaData.captchaId,
    };
  };

  const onFinish = () => {
    setLoading(true);
    const loginData = getLoginData();

    login(loginData)
      .then(async (res: any) => {
        const { user, token } = res.data;
        setToken(token);
        setUser(user);
        localStorage.setItem('language', 'zh');
        i18nScope.change('zh-CN');
        navigate('/home');
      })
      .catch((err) => {
        message.error(err);
        // getCaptcha();
        getAesKey();
      })
      .finally(() => {
        setLoading(false);
      });
  };

  // const getCaptcha = async () => {
  //   const res: CaptchaDataType = await getCaptchaData();
  //   setCaptchaData(res);
  // };
  useEffect(() => {
    // getCaptcha();
    getAesKey();
    persistor.purge();
    clear();
  }, []);

  const handleCancelLoginConfirm = () => {
    store.dispatch(setShowLoginConfirm(false));
    message.warning('Login cancelled');
    setDisableParea(true);
    form.resetFields();
    // getCaptcha();
  };

  /*   const ssoAuthRedirect = (provider: number) => {
    ajax
      .ssoAuth({
        provider: provider,
      })
      .then((res) => {
        const { redirectUrl } = res.data;
        window.location.href = redirectUrl;
      })
      .catch((err) => {
        console.error(err);
      });
  };
 */
  return (
    <ConfigProvider>
      <div className='flex justify-center items-center h-screen'>
        <Form
          form={form}
          name='login'
          initialValues={{ remember: true }}
          onFinish={onFinish}
          className='w-[280px]'
          style={{
            position: 'relative',
          }}>
          <img
            src='/DE/SVG/LOGO-Color.svg'
            alt='logo'
            className=' w-70 mx-auto mb-10'
          />
          <Form.Item
            name='coCode'
            rules={[
              {
                required: true,
                message: 'Please enter company code',
              },
            ]}>
            <Input
              placeholder='Project Code'
              className='w-[280px]'
              disabled={!disableParea}
            />
          </Form.Item>
          <Form.Item
            name='username'
            rules={[{ required: true, message: 'Please enter username' }]}>
            <Input placeholder='Username' id='username' className='w-[280px]' />
          </Form.Item>
          <Form.Item
            name='password'
            rules={[{ required: true, message: 'Please enter password' }]}>
            <Input.Password placeholder='Password' className='w-[280px]' />
          </Form.Item>

          {/* <Form.Item name='captcha' dependencies={['username', 'password']}>
          <Space.Compact>
            <Input
              placeholder='Captcha'
              style={{
                width: '180px',
                borderTopLeftRadius: '6px',
                borderBottomLeftRadius: '6px',
              }}
            />
            <img
              src={captchaData.picPath}
              alt='CAPTCHA'
              className='h-[32px] w-[100px] '
              style={{
                border: '1px solid #d9d9d9',
                borderTopRightRadius: '6px',
                borderBottomRightRadius: '6px',
              }}
              onClick={() => {
                getCaptcha();
                getAesKey();
              }}
            />
          </Space.Compact>
        </Form.Item> */}
          {!disableParea && (
            <Button
              className='w-[135px] mb-4 mr-[10px]'
              onClick={() => {
                setDisableParea(true);
                form.resetFields();
              }}>
              Reset
            </Button>
          )}
          <Button
            htmlType='submit'
            loading={loading}
            className={`mb-4 ${disableParea ? 'w-[280px]' : 'w-[135px]'}`}>
            Login
          </Button>
          <div
            style={{
              position: 'absolute',
              width: '100%',
              userSelect: 'none',
            }}>
            {/* <Collapse>
            <Collapse.Panel
              header={'使用第三方授权登录\Third-party Authorization Login'}
              key={0}>
              <Flex vertical={true} gap={10}>
                <Button onClick={() => ssoAuthRedirect(1)}>
                  钉钉登录\DingTalk Login
                </Button>
                <Button onClick={() => ssoAuthRedirect(2)}>
                  企业微信登录\WeWork Login
                </Button>
                <Button onClick={() => ssoAuthRedirect(3)}>
                  飞书登录\Feishu Login
                </Button>
              </Flex>
            </Collapse.Panel>
          </Collapse> */}
          </div>
        </Form>
        <Modal
          title={'Login Override'}
          open={useSelector((state: any) => state.login.showLoginConfirm)}
          onCancel={handleCancelLoginConfirm}
          okText={'Confirm'}
          cancelText={'Cancel'}
          onOk={() => {
            const loginData = getLoginData();
            setModalOkLoading(true);
            ajax
              .loginConfirm({
                ...loginData,
              })
              .then(() => {
                login(loginData)
                  .then(async (res: any) => {
                    const { user, token } = res.data;
                    setToken(token);
                    setUser(user);
                    localStorage.setItem('language', 'zh');
                    await i18nScope.change('zh');
                    navigate('/home');
                  })
                  .catch((err: any) => {
                    console.error(err);
                    // getCaptcha();
                    getAesKey();
                  })
                  .finally(() => {
                    setModalOkLoading(false);
                  });
              });
          }}
          okButtonProps={{
            loading: modalOkLoading,
          }}>
          This account is already logged in elsewhere. Do you want to override
          the login?
        </Modal>
      </div>
    </ConfigProvider>
  );
}

export default Login;
